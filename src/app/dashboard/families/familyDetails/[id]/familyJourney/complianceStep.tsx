import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Clock, Upload, FileText, DollarSign, ExternalLink } from 'lucide-react';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';

export default function ComplianceStep() {
  return (
    <div className="space-y-8">
      <Card className="border-l-8 border-l-gray-600 shadow-lg">
        <CardHeader className="pb-6">
          <CardTitle className="flex items-center space-x-3">
            <span className="bg-gray-600 text-white px-4 py-2 rounded-full text-sm font-bold">Step 3</span>
            <span className="text-2xl font-bold">Compliance (Income Check)</span>
          </CardTitle>
          <CardDescription className="text-lg text-gray-600">
            As a recognized sponsor, HBN must verify that both the Host Family and Au Pair meet all legal requirements.
          </CardDescription>
        </CardHeader>
      </Card>

      <Accordion type="single" collapsible defaultValue="item-3" className="space-y-4">
        <AccordionItem value="item-1" className="border-2 border-gray-200 rounded-lg">
          <AccordionTrigger className="text-left px-6 py-4 hover:no-underline">
            <div className="flex items-center space-x-4">
              <Clock className="h-6 w-6 text-gray-600" />
              <span className="text-lg font-semibold">3.1 Proof of Address</span>
              <Badge className="bg-gray-600 text-white">In Progress</Badge>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6">
            <Card className="border-0 shadow-lg">
              <CardContent className="pt-8">
                <div className="grid md:grid-cols-3 gap-8">
                  <div className="md:col-span-2 space-y-6">
                    <div>
                      <h4 className="font-bold text-black text-xl mb-3">LAW:</h4>
                      <p className="text-gray-700 text-lg leading-relaxed">
                        Under the Dutch Modern Migration Policy Act, recognized au pair agencies are legally required to
                        keep a BRP extract (official population register document) in their records.
                      </p>
                    </div>

                    <div>
                      <h4 className="font-bold text-black text-xl mb-3">To-Do:</h4>
                      <ul className="list-disc list-inside space-y-2 text-gray-700 text-lg">
                        <li>Request an official BRP extract from your local City Hall</li>
                        <li>Ensure that the BRP shows at least two people registered at the address</li>
                        <li>Make sure the BRP extract is not older than six months</li>
                      </ul>
                    </div>
                  </div>

                  <div className="bg-black text-white p-8 rounded-lg">
                    <h3 className="text-xl font-bold mb-6">BRP Extract</h3>
                    <p className="text-sm mb-6 text-gray-300">Upload maximum 1 file</p>
                    <div className="border-2 border-dashed border-white/30 rounded-lg p-8 text-center">
                      <Upload className="h-10 w-10 mx-auto mb-3 text-white/70" />
                      <p className="text-sm text-white/90">Drag and drop files or click to browse</p>
                    </div>
                    <Button className="w-full mt-6 bg-white text-black hover:bg-gray-100">PROCEED</Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="item-2" className="border-2 border-gray-200 rounded-lg">
          <AccordionTrigger className="text-left px-6 py-4 hover:no-underline">
            <div className="flex items-center space-x-4">
              <Clock className="h-6 w-6 text-gray-600" />
              <span className="text-lg font-semibold">3.2 Legal Residence</span>
              <Badge className="bg-gray-600 text-white">In Progress</Badge>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6">
            <Card className="border-0 shadow-lg">
              <CardContent className="pt-8">
                <div className="space-y-8">
                  <div>
                    <h4 className="font-bold text-black text-xl mb-3">Law:</h4>
                    <p className="text-gray-700 text-lg leading-relaxed">
                      According to IND regulations, a valid copy of the host parent's passport is required when applying
                      for an au pair residence permit.
                    </p>
                  </div>

                  <div className="grid md:grid-cols-2 gap-8">
                    <div className="bg-black text-white p-8 rounded-lg">
                      <h3 className="text-xl font-bold mb-6">Passport Parent 1</h3>
                      <div className="space-y-6">
                        <div className="border-2 border-dashed border-white/30 rounded-lg p-6 text-center">
                          <Upload className="h-8 w-8 mx-auto mb-3 text-white/70" />
                          <p className="text-sm text-white/90">Upload passport</p>
                        </div>
                        <div>
                          <Label htmlFor="bsn1" className="text-white text-lg font-semibold">
                            BSN NUMBER
                          </Label>
                          <Input
                            id="bsn1"
                            placeholder="Enter BSN number"
                            className="mt-2 bg-white/10 border-white/20 text-white placeholder:text-white/70 text-lg"
                          />
                        </div>
                      </div>
                    </div>

                    <div className="bg-gray-900 text-white p-8 rounded-lg">
                      <h3 className="text-xl font-bold mb-6">Passport Parent 2</h3>
                      <div className="space-y-6">
                        <div className="border-2 border-dashed border-white/30 rounded-lg p-6 text-center">
                          <Upload className="h-8 w-8 mx-auto mb-3 text-white/70" />
                          <p className="text-sm text-white/90">Upload passport</p>
                        </div>
                        <div>
                          <Label htmlFor="bsn2" className="text-white text-lg font-semibold">
                            BSN NUMBER
                          </Label>
                          <Input
                            id="bsn2"
                            placeholder="Enter BSN number"
                            className="mt-2 bg-white/10 border-white/20 text-white placeholder:text-white/70 text-lg"
                          />
                        </div>
                      </div>
                    </div>
                  </div>

                  <Button className="w-full bg-black text-white hover:bg-gray-800 text-lg py-3">SUBMIT</Button>
                </div>
              </CardContent>
            </Card>
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="item-3" className="border-2 border-black rounded-lg">
          <AccordionTrigger className="text-left px-6 py-4 hover:no-underline">
            <div className="flex items-center space-x-4">
              <Clock className="h-6 w-6 text-black" />
              <span className="text-lg font-semibold">3.3 Review Income</span>
              <Badge className="bg-black text-white">Active</Badge>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-6 pb-6">
            <Card className="border-0 shadow-lg">
              <CardContent className="pt-8">
                <div className="space-y-8">
                  <div className="grid md:grid-cols-2 gap-8">
                    <div>
                      <Label htmlFor="income-type" className="text-lg font-semibold text-black">
                        Main Income Type:
                      </Label>
                      <Select>
                        <SelectTrigger className="mt-2 text-lg">
                          <SelectValue placeholder="Select income type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="employed">Paid Employee (Loondienst)</SelectItem>
                          <SelectItem value="owner">Owner-Director of a BV</SelectItem>
                          <SelectItem value="self-employed">Self-employed</SelectItem>
                          <SelectItem value="private">Private Equity</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor="main-parent" className="text-lg font-semibold text-black">
                        Which parent provides the main income:
                      </Label>
                      <Select>
                        <SelectTrigger className="mt-2 text-lg">
                          <SelectValue placeholder="Select parent" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="parent1">Parent 1</SelectItem>
                          <SelectItem value="parent2">Parent 2</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="bg-gray-50 border-2 border-gray-200 p-6 rounded-lg">
                    <div className="flex items-center space-x-3 mb-4">
                      <DollarSign className="h-6 w-6 text-black" />
                      <h4 className="font-bold text-black text-xl">Income Requirements</h4>
                    </div>
                    <p className="text-gray-700 text-lg">
                      A Host Family must have sufficient, independent, and sustainable income above a certain threshold.
                      <a href="#" className="underline ml-2 text-black font-semibold">
                        Check the current income requirement here
                      </a>
                    </p>
                  </div>

                  <div className="grid md:grid-cols-3 gap-6">
                    <div className="bg-black text-white p-6 rounded-lg text-center">
                      <FileText className="h-10 w-10 mx-auto mb-4" />
                      <h4 className="font-bold mb-4 text-lg">Employer's Declaration</h4>
                      <Button variant="secondary" size="sm" className="mb-4 bg-white text-black hover:bg-gray-100">
                        <ExternalLink className="h-4 w-4 mr-2" />
                        DOWNLOAD
                      </Button>
                      <div className="border-2 border-dashed border-white/30 rounded-lg p-4">
                        <Upload className="h-6 w-6 mx-auto text-white/70" />
                      </div>
                    </div>

                    <div className="bg-gray-900 text-white p-6 rounded-lg text-center">
                      <FileText className="h-10 w-10 mx-auto mb-4" />
                      <h4 className="font-bold mb-4 text-lg">Employment Agreement</h4>
                      <div className="border-2 border-dashed border-white/30 rounded-lg p-8">
                        <Upload className="h-6 w-6 mx-auto text-white/70" />
                        <p className="text-xs mt-2 text-white/70">Upload file</p>
                      </div>
                    </div>

                    <div className="bg-gray-700 text-white p-6 rounded-lg text-center">
                      <FileText className="h-10 w-10 mx-auto mb-4" />
                      <h4 className="font-bold mb-4 text-lg">3 Recent Salary Slips</h4>
                      <div className="border-2 border-dashed border-white/30 rounded-lg p-8">
                        <Upload className="h-6 w-6 mx-auto text-white/70" />
                        <p className="text-xs mt-2 text-white/70">Upload files</p>
                      </div>
                    </div>
                  </div>

                  <Button className="w-full bg-black text-white hover:bg-gray-800 text-lg py-4">
                    SUBMIT INCOME DOCUMENTATION
                  </Button>
                </div>
              </CardContent>
            </Card>
          </AccordionContent>
        </AccordionItem>
      </Accordion>

      <div className="flex justify-end pt-8">
        <Button size="lg" className="bg-black text-white hover:bg-gray-800 px-8 py-3 text-lg">
          GO TO STEP 4
        </Button>
      </div>
    </div>
  );
}
