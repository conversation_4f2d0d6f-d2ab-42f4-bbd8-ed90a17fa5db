import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  Check,
  ChevronDown,
  Clock,
  ChevronLeft,
  ChevronRight,
  X,
  CheckCircle2,
  Circle
} from 'lucide-react';
import { cn } from '@/lib/utils';
import React, { useState, useMemo } from 'react';

interface Step {
  id: string;
  title: string;
  description: string;
  type: string;
  icon: React.ElementType;
}

interface Stage {
  id: number;
  title: string;
  description: string;
  icon: React.ElementType;
  steps: Step[];
}

interface SidebarContentProps {
  stages: Stage[];
  currentStage: number;
  currentStep: number;
  expandedStages: number[];
  stepStatus: { [key: string]: boolean };
  toggleStageExpansion: (stageId: number) => void;
  goToStep: (stageId: number, stepIndex: number) => void;
  isMobileMenuOpen?: boolean;
  className?: string;
  onCollapsedChange?: (collapsed: boolean) => void;
  initialCollapsed?: boolean;
  closeMobileMenu?: () => void; // New prop for direct mobile menu closing
}

const SidebarContent: React.FC<SidebarContentProps> = ({
  stages,
  currentStage,
  currentStep,
  expandedStages,
  stepStatus,
  toggleStageExpansion,
  goToStep,
  isMobileMenuOpen = false,
  className,
  onCollapsedChange,
  initialCollapsed = false,
  closeMobileMenu // Destructure the new prop
}) => {
  // Use local state for each sidebar's collapsed state instead of global context
  const [sidebarCollapsed, setSidebarCollapsed] = useState(initialCollapsed);

  // Calculate overall progress
  const progressData = useMemo(() => {
    const allSteps = stages.flatMap(stage => stage.steps);
    const completedSteps = allSteps.filter(step => stepStatus[step.id]);
    const totalSteps = allSteps.length;
    const completedCount = completedSteps.length;
    const progressPercentage = totalSteps > 0 ? Math.round((completedCount / totalSteps) * 100) : 0;

    return {
      totalSteps,
      completedCount,
      progressPercentage,
      currentStageProgress: stages.find(s => s.id === currentStage)?.steps.filter(step => stepStatus[step.id]).length || 0,
      currentStageTotal: stages.find(s => s.id === currentStage)?.steps.length || 0
    };
  }, [stages, stepStatus, currentStage]);

  // Notify parent component when collapse state changes
  const toggleSidebar = () => {
    const newState = !sidebarCollapsed;
    setSidebarCollapsed(newState);
    if (onCollapsedChange) {
      onCollapsedChange(newState);
    }
  };
  
  // Function to handle mobile sidebar backdrop click
  const handleBackdropClick = (e: React.MouseEvent) => {
    // Prevent event propagation
    e.stopPropagation();
    
    // Only handle if mobile menu is open
    if (isMobileMenuOpen) {
      if (closeMobileMenu) {
        closeMobileMenu(); // Use the new prop if available
      } else if (onCollapsedChange) {
        onCollapsedChange(true); // Fallback to existing behavior
      }
    }
  };

  return (
    <TooltipProvider>
      <aside
        className={cn(
          'h-full w-full flex flex-col bg-background',
          className
        )}
      >
        {/* Mobile close button - only visible on mobile when sidebar is open */}
        {isMobileMenuOpen && (
          <div className="flex justify-end p-2 lg:hidden">
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8"
              onClick={handleBackdropClick}
              aria-label="Close sidebar"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        )}

        {/* Desktop collapse/expand button - only visible on desktop */}
        {!isMobileMenuOpen && (
          <button
            className={cn(
              'absolute top-4 -left-3 z-30 bg-background border border-border/60 rounded-full p-1.5 shadow-md',
              'transition-all duration-200 hover:shadow-lg hidden lg:flex lg:items-center lg:justify-center',
              'transform hover:scale-105 active:scale-95'
            )}
            style={{ left: '-16px' }}
            onClick={toggleSidebar}
            aria-label={sidebarCollapsed ? 'Expand sidebar' : 'Collapse sidebar'}
          >
            {sidebarCollapsed ? <ChevronLeft size={16} /> : <ChevronRight size={16} />}
          </button>
        )}

        {/* Enhanced Sidebar Header with Progress */}
        {!sidebarCollapsed && !isMobileMenuOpen && (
          <div className="p-4 border-b border-border/40 bg-gradient-to-r from-primary/5 to-purple-500/5">
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <h3 className="font-semibold text-sm text-foreground">Journey Progress</h3>
                <Badge variant="secondary" className="text-xs">
                  {progressData.progressPercentage}%
                </Badge>
              </div>

              <div className="space-y-2">
                <Progress
                  value={progressData.progressPercentage}
                  className="h-2 bg-muted"
                />
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>{progressData.completedCount} of {progressData.totalSteps} completed</span>
                  <span className="flex items-center gap-1">
                    <CheckCircle2 className="h-3 w-3 text-green-600" />
                    {progressData.completedCount}
                  </span>
                </div>
              </div>

              {/* Current Stage Mini Progress */}
              <div className="pt-2 border-t border-border/20">
                <div className="flex items-center justify-between text-xs">
                  <span className="text-muted-foreground">Current Stage</span>
                  <span className="font-medium">
                    {progressData.currentStageProgress}/{progressData.currentStageTotal}
                  </span>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Collapsed State Progress Indicator */}
        {sidebarCollapsed && !isMobileMenuOpen && (
          <div className="p-2 border-b border-border/40">
            <div className="flex flex-col items-center space-y-2">
              <div className="relative">
                <Circle className="h-8 w-8 text-muted-foreground" />
                <div className="absolute inset-0 flex items-center justify-center">
                  <span className="text-xs font-bold text-primary">
                    {progressData.progressPercentage}
                  </span>
                </div>
              </div>
              <div className="w-full bg-muted rounded-full h-1">
                <div
                  className="bg-primary h-1 rounded-full transition-all duration-300"
                  style={{ width: `${progressData.progressPercentage}%` }}
                />
              </div>
            </div>
          </div>
        )}

        <ScrollArea className="flex-1">
          <div className={cn("px-3 py-4 lg:p-4 space-y-3", sidebarCollapsed ? "px-2" : "")}>
            {stages.map(stage => {
              const isCurrentStage = stage.id === currentStage;
              const isExpanded = expandedStages.includes(stage.id);
              const StageIcon = stage.icon;
              const stageCompleted = stage.steps.every(step => stepStatus[step.id]);

              // Calculate stage progress
              const stageProgress = stage.steps.filter(step => stepStatus[step.id]).length;
              const stageTotal = stage.steps.length;
              const stageProgressPercentage = stageTotal > 0 ? Math.round((stageProgress / stageTotal) * 100) : 0;

              return (
                <div key={stage.id} className="w-full group">
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant={isCurrentStage ? 'secondary' : 'ghost'}
                        className={cn(
                          'w-full h-auto py-3 px-3 text-left relative rounded-xl transition-all duration-300',
                          'border border-transparent hover:border-border/50',
                          isCurrentStage
                            ? 'bg-gradient-to-r from-primary/10 to-purple-500/10 shadow-md border-primary/20'
                            : 'hover:bg-gradient-to-r hover:from-muted/60 hover:to-muted/40',
                          stageCompleted
                            ? 'bg-gradient-to-r from-green-50 to-emerald-50 border-green-200/50 text-green-800'
                            : '',
                          sidebarCollapsed ? 'justify-center px-2 py-2' : '',
                          'group-hover:shadow-sm'
                        )}
                        onClick={() => toggleStageExpansion(stage.id)}
                      >
                        <div className={cn(
                          "flex items-center gap-3 min-w-0 w-full",
                          !sidebarCollapsed ? "pr-8" : ""
                        )}>
                          {/* Enhanced Stage Icon with Status */}
                          <div className="relative">
                            <div
                              className={cn(
                                'w-8 h-8 lg:w-9 lg:h-9 rounded-xl flex items-center justify-center flex-shrink-0 transition-all duration-300',
                                'shadow-sm border',
                                stageCompleted
                                  ? 'bg-gradient-to-br from-green-500 to-emerald-600 text-white border-green-400 shadow-green-200'
                                  : isCurrentStage
                                    ? 'bg-gradient-to-br from-primary to-purple-600 text-white border-primary/30 shadow-primary/20'
                                    : 'bg-gradient-to-br from-muted to-muted/80 text-muted-foreground border-border group-hover:from-muted/80 group-hover:to-muted/60'
                              )}
                            >
                              {stageCompleted ? (
                                <CheckCircle2 className="h-4 w-4 lg:h-5 lg:w-5" />
                              ) : (
                                <StageIcon className="h-4 w-4 lg:h-5 lg:w-5" />
                              )}
                            </div>

                            {/* Progress Ring for Current Stage */}
                            {isCurrentStage && !stageCompleted && !sidebarCollapsed && (
                              <div className="absolute -inset-1">
                                <div className="w-full h-full rounded-xl border-2 border-primary/30 animate-pulse" />
                              </div>
                            )}
                          </div>

                          {!sidebarCollapsed && (
                            <div className="min-w-0 flex-1 space-y-1">
                              <div className="flex items-center justify-between">
                                <div className="font-semibold text-sm lg:text-base truncate">
                                  {stage.title}
                                </div>
                                {stageCompleted && (
                                  <Badge variant="secondary" className="text-xs bg-green-100 text-green-700 border-green-200">
                                    Complete
                                  </Badge>
                                )}
                              </div>

                              <div className="text-xs text-muted-foreground truncate">
                                {stage.description}
                              </div>

                              {/* Stage Progress Bar */}
                              <div className="flex items-center gap-2 pt-1">
                                <div className="flex-1 bg-muted/60 rounded-full h-1.5">
                                  <div
                                    className={cn(
                                      "h-1.5 rounded-full transition-all duration-500",
                                      stageCompleted
                                        ? "bg-gradient-to-r from-green-500 to-emerald-500"
                                        : "bg-gradient-to-r from-primary to-purple-500"
                                    )}
                                    style={{ width: `${stageProgressPercentage}%` }}
                                  />
                                </div>
                                <span className="text-xs font-medium text-muted-foreground min-w-fit">
                                  {stageProgress}/{stageTotal}
                                </span>
                              </div>
                            </div>
                          )}
                        </div>

                        {/* Enhanced Expand/Collapse Indicator */}
                        {!sidebarCollapsed && (
                          <div className="absolute right-3 top-1/2 -translate-y-1/2">
                            <ChevronDown
                              className={cn(
                                'h-4 w-4 transition-all duration-300 text-muted-foreground',
                                'group-hover:text-foreground',
                                isExpanded ? 'rotate-180' : ''
                              )}
                            />
                          </div>
                        )}
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent side="left" className="max-w-xs hidden lg:block">
                      <div className="space-y-2">
                        <div>
                          <div className="font-semibold">{stage.title}</div>
                          <div className="text-xs text-muted-foreground">{stage.description}</div>
                        </div>
                        <div className="flex items-center justify-between text-xs">
                          <span>Progress:</span>
                          <span className="font-medium">{stageProgressPercentage}% ({stageProgress}/{stageTotal})</span>
                        </div>
                      </div>
                    </TooltipContent>
                  </Tooltip>

                  {isExpanded && !sidebarCollapsed && (
                    <div className="ml-8 lg:ml-10 pl-4 border-l-2 border-gradient-to-b from-primary/20 to-purple-500/20 mt-3 mb-4 space-y-2">
                      {stage.steps.map((step, stepIndex) => {
                        const StepIcon = step.icon;
                        const isCurrentStep = isCurrentStage && stepIndex === currentStep;
                        const isStepCompleted = stepStatus[step.id];

                        return (
                          <Tooltip key={step.id}>
                            <TooltipTrigger asChild>
                              <Button
                                variant="ghost"
                                size="sm"
                                className={cn(
                                  'w-full justify-start h-auto py-2.5 px-3 text-left rounded-lg transition-all duration-200',
                                  'border border-transparent hover:border-border/30',
                                  isCurrentStep
                                    ? 'bg-gradient-to-r from-primary/15 to-purple-500/15 shadow-sm border-primary/30'
                                    : 'hover:bg-gradient-to-r hover:from-muted/40 hover:to-muted/20',
                                  isStepCompleted
                                    ? 'bg-gradient-to-r from-green-50/80 to-emerald-50/80 border-green-200/30'
                                    : '',
                                  'group/step hover:shadow-sm'
                                )}
                                onClick={() => goToStep(stage.id, stepIndex)}
                              >
                                <div className="flex items-center gap-3 w-full min-w-0">
                                  {/* Enhanced Step Icon */}
                                  <div className="relative">
                                    <div
                                      className={cn(
                                        'w-6 h-6 lg:w-7 lg:h-7 rounded-lg flex items-center justify-center flex-shrink-0 transition-all duration-200',
                                        'shadow-sm border',
                                        isStepCompleted
                                          ? 'bg-gradient-to-br from-green-400 to-emerald-500 text-white border-green-300 shadow-green-100'
                                          : isCurrentStep
                                            ? 'bg-gradient-to-br from-primary/80 to-purple-500/80 text-white border-primary/40 shadow-primary/10'
                                            : 'bg-gradient-to-br from-muted/80 to-muted/60 text-muted-foreground border-border/60 group-hover/step:from-muted/60 group-hover/step:to-muted/40'
                                      )}
                                    >
                                      {isStepCompleted ? (
                                        <Check className="h-3 w-3 lg:h-3.5 lg:w-3.5" />
                                      ) : (
                                        <StepIcon className="h-3 w-3 lg:h-3.5 lg:w-3.5" />
                                      )}
                                    </div>

                                    {/* Current Step Indicator */}
                                    {isCurrentStep && !isStepCompleted && (
                                      <div className="absolute -inset-0.5">
                                        <div className="w-full h-full rounded-lg border border-primary/40 animate-pulse" />
                                      </div>
                                    )}
                                  </div>

                                  <div className="flex-1 min-w-0 space-y-0.5">
                                    <div className="flex items-center justify-between">
                                      <div className={cn(
                                        "text-sm font-medium truncate",
                                        isStepCompleted ? "text-green-800" : isCurrentStep ? "text-foreground" : "text-foreground/80"
                                      )}>
                                        {step.title}
                                      </div>

                                      {/* Step Status Indicators */}
                                      <div className="flex items-center gap-1">
                                        {isStepCompleted && (
                                          <Badge variant="secondary" className="text-xs bg-green-100 text-green-700 border-green-200 px-1.5 py-0">
                                            Done
                                          </Badge>
                                        )}
                                        {isCurrentStep && !isStepCompleted && (
                                          <div className="flex items-center gap-1">
                                            <Clock className="h-3 w-3 text-primary animate-pulse" />
                                            <Badge variant="secondary" className="text-xs bg-primary/10 text-primary border-primary/20 px-1.5 py-0">
                                              Active
                                            </Badge>
                                          </div>
                                        )}
                                      </div>
                                    </div>

                                    <div className="text-xs text-muted-foreground truncate">
                                      {step.description}
                                    </div>
                                  </div>
                                </div>
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent side="left" className="max-w-xs hidden lg:block">
                              <div className="space-y-2">
                                <div>
                                  <div className="font-semibold">{step.title}</div>
                                  <div className="text-xs text-muted-foreground">{step.description}</div>
                                </div>
                                <div className="flex items-center gap-2 text-xs">
                                  <span className={cn(
                                    "px-2 py-1 rounded-md font-medium",
                                    isStepCompleted
                                      ? "bg-green-100 text-green-700"
                                      : isCurrentStep
                                        ? "bg-primary/10 text-primary"
                                        : "bg-muted text-muted-foreground"
                                  )}>
                                    {isStepCompleted ? "Completed" : isCurrentStep ? "In Progress" : "Pending"}
                                  </span>
                                </div>
                              </div>
                            </TooltipContent>
                          </Tooltip>
                        );
                      })}
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </ScrollArea>
      </aside>
    </TooltipProvider>
  );
};

export default SidebarContent;
