import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { AlertCircle } from 'lucide-react';

export default function DepartureStep() {
  return (
    <div className="space-y-8">
      <Card className="border-l-8 border-l-gray-400 shadow-lg">
        <CardHeader className="pb-6">
          <CardTitle className="flex items-center space-x-3">
            <span className="bg-gray-400 text-white px-4 py-2 rounded-full text-sm font-bold">Step 9</span>
            <span className="text-2xl font-bold">Departure</span>
          </CardTitle>
          <CardDescription className="text-lg text-gray-600">
            Your Au Pair's year is coming to an end, and we hope you both can look back on a memorable cultural exchange
            experience.
          </CardDescription>
        </CardHeader>
      </Card>

      <Card className="border-2 border-gray-300 bg-gray-50 shadow-lg">
        <CardContent className="pt-8 pb-8">
          <div className="text-center py-12">
            <AlertCircle className="h-20 w-20 text-gray-400 mx-auto mb-6" />
            <h3 className="text-2xl font-bold text-gray-800 mb-4">Step Not Available</h3>
            <p className="text-gray-600 mb-6 text-lg max-w-md mx-auto">
              This step will become available near the end of your au pair's program.
            </p>
            <Badge variant="secondary" className="bg-gray-200 text-gray-700 px-4 py-2 text-lg">
              Pending Previous Steps
            </Badge>
          </div>
        </CardContent>
      </Card>

      <div className="flex justify-between pt-8">
        <Button
          variant="outline"
          size="lg"
          className="border-black text-black hover:bg-black hover:text-white px-8 py-3 text-lg"
        >
          GO BACK TO STEP 8
        </Button>
        <Button size="lg" className="bg-gray-400 text-white px-8 py-3 text-lg" disabled>
          COMPLETE JOURNEY
        </Button>
      </div>
    </div>
  );
}
